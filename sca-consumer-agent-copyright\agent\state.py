from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field


# LangGraph 状态定义
class InputState(BaseModel):
    """输入状态定义"""
    user_requirement: str = Field(description="用户需求描述")


class OutputState(BaseModel):
    """输出状态定义"""
    # 来自 compress_and_upload_node
    oss_key: Optional[str] = Field(default=None, description="OSS文件路径")
    deployment_url: Optional[str] = Field(default=None, description="部署URL")


class OverallState(InputState, OutputState):
    """整体状态定义，包含所有中间处理结果，按节点执行顺序排列"""
    
    # initialize_sandbox - 初始化沙箱环境
    sandbox_dir: Optional[str] = Field(default=None, description="沙箱根目录路径")
    project_name: Optional[str] = Field(default=None, description="项目名称")
    project_dir: Optional[str] = Field(default=None, description="代码目录路径")
    images_dir: Optional[str] = Field(default=None, description="图片目录路径")
    docs_dir: Optional[str] = Field(default=None, description="文档目录路径")
    
    # create_requirement_outline - 创建需求大纲
    requirement_outline: Optional[Dict[str, Any]] = Field(default=None, description="需求大纲JSON字符串")
    
    # generate_page_code - 生成页面代码
    page_codes: Optional[Dict[str, Any]] = Field(default=None, description="所有页面代码列表")
    pages: Optional[List[Dict[str, Any]]] = Field(default=None, description="页面信息列表")
    
    # deploy_and_check - 部署和检查节点状态
    page_check_attempts: int = Field(default=0, description="页面检查尝试次数")
    max_page_check_attempts: int = Field(default=20, description="最大页面检查尝试次数")
    page_error_mapping: Dict[str, List[str]] = Field(default_factory=dict, description="页面路径到异常信息列表的映射")
    has_page_errors: bool = Field(default=False, description="是否存在页面异常")
    
    
    # capture_screenshots - 截取屏幕截图
    screenshot_results: Optional[Dict[str, str]] = Field(default=None, description="页面截图结果字典，键为页面名称，值为截图路径")
    
    # generate_apply_doc - 生成软件著作权申请文档
    copyright_apply_doc_path: Optional[str] = Field(default=None, description="软件著作权申请文档路径")
    copyright_apply_str: Optional[str] = Field(default=None, description="软件著作权申请信息")
    
    # generate_code_doc - 生成代码文档
    code_doc_path: Optional[str] = Field(default=None, description="代码文档路径")
    
    # generate_usage_doc - 生成使用说明文档
    usage_doc_path: Optional[str] = Field(default=None, description="使用说明文档路径")
    
    # generate_design_doc - 生成设计说明书
    design_doc_path: Optional[str] = Field(default=None, description="设计说明书路径")
    
    # 工作流控制变量
    current_node_name: Optional[str] = Field(default=None, description="当前运行的节点名称")