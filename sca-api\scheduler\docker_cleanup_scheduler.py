from omni.log.log import olog
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler
from agent.workflow2.utils.docker_image_builder import stop_old_sca_sandbox_containers


@register_scheduler(trigger='cron', hour='*', minute=0)
class DockerCleanupScheduler(BaseScheduler):
    def run_task(self):
        olog.info('开始执行定时清理旧沙箱容器任务')
        try:
            success, message = stop_old_sca_sandbox_containers()
            if success:
                olog.info(f"旧沙箱容器清理成功: {message}")
            else:
                olog.error(f"旧沙箱容器清理失败: {message}")
        except Exception as e:
            olog.exception(f"定时清理旧沙箱容器任务执行异常: {e}") 