from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict
from repository.models import AuthorizationCode, User
import time
import uuid


@register_handler("authorization_code")
class AuthorizationCodeApi:

    @auth_required(["admin"])
    def create(self, data):
        # 自动生成授权码
        code = f"SCA-{uuid.uuid4().hex[:12].upper()}"
        
        # 确保生成的授权码不重复
        while AuthorizationCode.objects(code=code).first():
            code = f"SCA-{uuid.uuid4().hex[:12].upper()}"

        # 创建新授权码
        authorization_code = AuthorizationCode(
            code=code,
            status="未使用",
            create_at=int(time.time())
        )
        authorization_code.save()
        return doc_to_dict(authorization_code)

    @auth_required(["admin"])
    def query_all(self, data):
        page = int(data.get("page", 1))
        page_size = int(data.get("page_size", 10))
        
        # 计算总数
        total = AuthorizationCode.objects().count()
        
        # 获取分页数据
        authorization_codes = AuthorizationCode.objects().order_by("-create_at").skip((page - 1) * page_size).limit(page_size)
        
        # 转换为字典并添加用户名
        result_data = []
        for code in authorization_codes:
            code_dict = doc_to_dict(code)
            if code.user_id:
                user = User.objects(id=code.user_id).first()
                if user:
                    code_dict["username"] = user.username
            result_data.append(code_dict)
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": result_data
        }
        
    @auth_required(["user", "admin"])
    def get_available_codes(self, data):
        """获取可用的授权码列表"""
        # 查询所有未使用的授权码
        available_codes = AuthorizationCode.objects(status="未使用").order_by("-create_at")
        
        return {
            "total": available_codes.count(),
            "data": docs_to_dict(available_codes)
        }