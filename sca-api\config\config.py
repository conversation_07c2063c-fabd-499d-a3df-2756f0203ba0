import os

sca_docs_oss_key = "/sca/docs"

# Docker部署配置
process_env = os.getenv("process_env")
if process_env == "dev":
    DOCKER_CONFIG = {
        "dockerfile_path": os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "agent",
            "workflow2",
            "sandbox_deploy",
            "Dockerfile",
        ),
        "remote": {
            "host": "**************",
            "port": 22,
            "username": "root",
            "password": "fuck2you@",
            "remote_dir": "/tmp/sca-remote",
        },
        "port_range": {"start": 20000, "end": 30000},
    }
if process_env == "prod":
    DOCKER_CONFIG = {
        "dockerfile_path": os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "agent",
            "workflow2",
            "sandbox_deploy",
            "Dockerfile",
        ),
        "remote": {
            "host": "*************",
            "port": 22,
            "username": "root",
            "password": "fuck2you@",
            "remote_dir": "/tmp/sca-remote",
        },
        "port_range": {"start": 20000, "end": 30000},
    }
