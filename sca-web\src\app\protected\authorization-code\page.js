"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Button,
  Stack,
  Card,
  CardContent,
  CardActions,
} from "@mui/material";
import { Plus, Copy } from "lucide-react";
import { useDispatch } from "react-redux";
import { addAlert } from "@/core/components/redux/alert-slice";
import { AlertType, AlertMsg } from "@/core/components/alert";
import { InfiniteScrollList } from "@/core/components/InfiniteScrollList";
import { authorizationCodeApi } from "@/api/authorization-code-api";

export default function AuthorizationCodePage() {
  const dispatch = useDispatch();
  const [items, setItems] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [creating, setCreating] = useState(false);
  const pageSize = 10;

  const fetchAuthCodes = useCallback(async (pageNum) => {
    try {
      console.log('fetchAuthCodes调用，请求页码:', pageNum);
      
      const response = await authorizationCodeApi.queryAll(
        pageNum,
        pageSize
      );
      
      if (response && response.data) {
        if (pageNum === 1) {
          setItems(response.data);
        } else {
          setItems(prev => [...prev, ...response.data]);
        }
        
        setTotal(response.total);
        
        // 更新页码状态
        setPage(pageNum);
        
        console.log('API返回的页码:', response.page);
        console.log('手动设置的页码:', pageNum);
        
        // 根据数据长度判断是否还有更多数据
        setHasMore(response.data.length === pageSize);
      }
    } catch (error) {
      dispatch(addAlert({ type: AlertType.ERROR, message: `获取授权码失败: ${error.message || "未知错误"}` }));
    }
  }, [dispatch, pageSize]);

  useEffect(() => {
    fetchAuthCodes(1);
  }, [fetchAuthCodes]);

  const handleCopyCode = (code) => {
    navigator.clipboard.writeText(code)
      .then(() => {
        dispatch(addAlert({ type: AlertType.SUCCESS, message: "授权码已复制到剪贴板" }));
      })
      .catch(() => {
        dispatch(addAlert({ type: AlertType.ERROR, message: "复制失败，请手动复制" }));
      });
  };

  const handleCreateCode = async () => {
    try {
      setCreating(true);
      await authorizationCodeApi.create();
      dispatch(addAlert({ type: AlertType.SUCCESS, message: AlertMsg.CREATE }));
      // 重置页码并重新获取第一页数据
      setItems([]);
      setPage(1);
      fetchAuthCodes(1);
    } catch (error) {
      dispatch(addAlert({ type: AlertType.ERROR, message: `创建授权码失败: ${error.message || "未知错误"}` }));
    } finally {
      setCreating(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "未使用":
        return "success";
      case "已使用":
        return "primary";
      default:
        return "default";
    }
  };

  const loadMore = useCallback(async () => {
    console.log('loadMore被调用');
    console.log('当前页码:', page);
    const nextPage = page + 1;
    console.log('计算出的nextPage:', nextPage);
    await fetchAuthCodes(nextPage);
  }, [fetchAuthCodes, page]);

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "";
    return new Date(timestamp * 1000).toLocaleDateString('zh-CN');
  };

  // 渲染授权码卡片
  const renderAuthCodeCard = (item) => {
    return (
      <Card key={item.id} sx={{ height: '100%', display: 'flex', flexDirection: 'column', width: '100%' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
            <Chip 
              label={item.status} 
              color={getStatusColor(item.status)} 
              size="small" 
            />
            <Typography variant="caption" color="text.secondary">
              创建时间: {formatTimestamp(item.create_at)}
            </Typography>
          </Stack>
          
          <Typography variant="h6" component="div" sx={{ mb: 1, wordBreak: "break-all" }}>
            {item.code}
          </Typography>
          
          {item.user_id && (
            <>
              <Typography variant="body2">
                使用者账号: {item.username || "未知用户"}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                使用时间: {formatTimestamp(item.used_at)}
              </Typography>
            </>
          )}
        </CardContent>
        <CardActions>
          <Button
            size="small"
            startIcon={<Copy size={16} />}
            onClick={() => handleCopyCode(item.code)}
            fullWidth
          >
            复制授权码
          </Button>
        </CardActions>
      </Card>
    );
  };

  // 添加组件重新渲染时的日志
  useEffect(() => {
    console.log('组件渲染，当前page状态:', page);
  });

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, width: '100%', maxWidth: '100%' }}>
      <Box>
        <Typography variant="h5" component="h1" gutterBottom>
          授权码管理
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          在这里您可以查看和管理所有的软件授权码
        </Typography>
        
        <Stack 
          direction={{ xs: "column", md: "row" }} 
          spacing={2} 
          sx={{ mb: 3, mt: 2 }}
          justifyContent="space-between"
          alignItems={{ xs: "stretch", md: "center" }}
          width="100%"
        >
          <Typography variant="body2" color="text.secondary">
            共 {total} 个授权码
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<Plus size={18} />}
            onClick={handleCreateCode}
            disabled={creating}
          >
            {creating ? "生成中..." : "生成新授权码"}
          </Button>
        </Stack>

        <Box sx={{ mt: 3, width: '100%' }}>
          <InfiniteScrollList
            items={items}
            renderItem={renderAuthCodeCard}
            loadMore={loadMore}
            hasMore={hasMore}
            gridTemplateColumns={{
              xs: "1fr",
              sm: "1fr 1fr",
              md: "1fr 1fr 1fr"
            }}
          />
        </Box>
      </Box>
    </Box>
  );
}
