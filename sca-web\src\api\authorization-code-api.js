import api from "@/core/api/api";

export const authorizationCodeApi = {
  /**
   * 创建授权码
   * @returns {Promise<Object>} 创建结果
   */
  create: async () => {
    return await api({
      resource: "authorization_code",
      method_name: "create",
    });
  },

  /**
   * 查询授权码列表
   * @param {number} page 页码
   * @param {number} page_size 每页数量
   * @returns {Promise<Object>} 包含分页信息和授权码列表的对象
   */
  queryAll: async (page = 1, page_size = 10) => {
    return await api({
      resource: "authorization_code",
      method_name: "query_all",
      page,
      page_size,
    });
  },
  
  /**
   * 获取所有可用的授权码
   * @returns {Promise<Object>} 包含可用授权码列表的对象
   */
  getAvailableCodes: async () => {
    return await api({
      resource: "authorization_code",
      method_name: "get_available_codes",
    });
  },
};