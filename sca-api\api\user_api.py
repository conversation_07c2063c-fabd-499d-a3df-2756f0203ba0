import json
import time

from omni.api.auth import auth_required, generate_token
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict
from omni.redis.redis_client import rc
from repository.models import User


@register_handler("user")
class UserApi:

    def login(self, data):
        username = data.get("username")
        password = data.get("password")
        if not username or not password:
            raise MException("用户名和密码不能为空")

        # 登录验证
        user = User.objects(username=username).first()
        if not user:
            raise MException("登录失败,错误的用户名或密码")
        if user["password"] != password:
            raise MException("登录失败,错误的用户名或密码")
        
        user = doc_to_dict(user)
        access_token = generate_token(user["id_"])
        roles = user["roles"]
        return {"access_token": access_token, "roles": roles}

    def register(self, data):
        """注册新用户"""
        phone = data.get("phone")
        password = data.get("password")
        
        if not phone or not password:
            raise MException("手机号和密码不能为空")
            
        # 验证手机号格式
        if not phone.isdigit() or len(phone) != 11 or not phone.startswith("1"):
            raise MException("请输入有效的手机号码")
            
        # 检查密码长度
        if len(password) < 8:
            raise MException("密码长度不能少于8个字符")
            
        # 检查用户是否已存在
        existing_user = User.objects(username=phone).first()
        if existing_user:
            raise MException("该手机号已被注册")
            
        # 创建新用户
        new_user = User(
            username=phone,
            password=password,
            roles=["user"],
            create_at=int(time.time())
        )
        new_user.save()
        
        olog.info(f"新用户注册成功: {phone}")
        return {"success": True, "message": "注册成功"}

    @auth_required(["admin"])  # 只允许管理员删除用户
    def delete(self, data):
        user_id = data.get("id_")
        user = User.objects(id=user_id).first()
        if not user:
            raise MException("用户不存在")
        user.delete()
        return {"message": "用户已成功删除"}

    @auth_required(["user", "admin"])
    def query_one(self, data):
        user_id = data.get("user_id")
        user = User.objects(id=user_id).first()
        return doc_to_dict(user)

    @auth_required(["admin"])
    def query_all(self, data):
        page = int(data.get("page", 1))
        page_size = int(data.get("pageSize", 10))
        search_term = data.get("searchTerm", "")
        
        # 计算跳过的记录数
        skip = (page - 1) * page_size
        
        # 构建查询条件
        query = {}
        if search_term:
            query["username__icontains"] = search_term
        
        # 查询总数
        total = User.objects(**query).count()
        
        # 执行分页查询
        users = User.objects(**query).skip(skip).limit(page_size)
        
        result = []
        for user in users:
            user_dict = doc_to_dict(user)
            # 将roles列表转换为角色中文名
            roles_map = {"admin": "管理员", "user": "普通用户"}
            user_dict["role"] = "、".join([roles_map.get(role, role) for role in user_dict.get("roles", [])])
                
            result.append(user_dict)
            
        return {"data": result, "total": total}

    @auth_required(["user", "admin"])
    def get_roles(self, data):
        """获取用户角色，使用 Redis 缓存"""
        # 系统会自动获取当前用户的user_id，不需要从data中获取
        user_id = data.get("user_id")  # 这里的user_id是系统自动注入的当前用户ID

        # Redis 缓存键
        redis_key = f"session:roles:{user_id}"

        # 尝试从缓存获取角色
        cached_roles = rc.get(redis_key)
        if cached_roles:
            olog.info(f"从 Redis 获取用户 {user_id} 的角色缓存")
            return {"roles": json.loads(cached_roles)}

        # 缓存不存在，从数据库查询
        user = User.objects(id=user_id).first()
        roles = doc_to_dict(user).get("roles", [])

        # 将角色存入 Redis 缓存，设置过期时间 (例如：5 分钟)
        rc.setex(redis_key, 300, json.dumps(roles))
        olog.info(f"从数据库获取用户 {user_id} 的角色并存入 Redis 缓存")

        return {"roles": roles}
