import api from "@/core/api/api";

export const copyrightTaskApi = {
  /**
   * 创建软著任务
   * @param {string} name - 任务名称
   * @param {string} description - 任务描述
   * @param {string} authorization_code - 授权码
   * @returns {Promise<Object>} 创建结果
   */
  create: async (name, description, authorization_code) => {
    return await api({
      resource: "copyright_task",
      method_name: "create",
      name,
      description,
      authorization_code,
    });
  },

  /**
   * 查询软著任务列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.page_size 每页数量
   * @returns {Promise<Object>} 包含分页信息和任务列表的对象
   */
  queryList: async (params = { page: 1 }) => {
    return await api({
      resource: "copyright_task",
      method_name: "query_list",
      ...params
    });
  },
  
  /**
   * AI智能完善描述
   * @param {string} description - 原始描述
   * @returns {Promise<Object>} 包含扩展后描述的对象
   */
  aiExpandDescription: async (description) => {
    return await api({
      resource: "copyright_task",
      method_name: "ai_expand_description",
      description,
    });
  },
}; 