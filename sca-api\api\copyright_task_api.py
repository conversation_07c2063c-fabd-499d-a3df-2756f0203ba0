import time
from concurrent.futures import Thread<PERSON>oolExecutor

from omni.api.auth import auth_required
from omni.api.exception import MException
from omni.api.handler_register import register_handler
from omni.log.log import olog
from omni.mongo.mongo_client import doc_to_dict, docs_to_dict
from repository.models import CopyrightTask, AuthorizationCode
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from omni.llm.chat.chat_model_factory import ChatModelFactory
from agent.workflow2.workflow_definition import run_workflow1
from omni.integration.oss.tencent_oss import OSSClient, SignedMethod


@register_handler("copyright_task")
class CopyrightTaskApi:

    @auth_required(["user", "admin"])
    def create(self, data):
        # 获取必要的字段
        user_id = data.get("user_id")  # 用户ID会自动注入
        name = data.get("name")
        description = data.get("description")
        authorization_code = data.get("authorization_code")

        if not description:
            raise MException("任务描述不能为空")
            
        if not authorization_code:
            raise MException("授权码不能为空")
            
        # 验证授权码
        auth_code_obj = AuthorizationCode.objects(code=authorization_code, status="未使用").first()
        if not auth_code_obj:
            raise MException("授权码无效或已使用")
            
        # 消费授权码
        auth_code_obj.status = "已使用"
        auth_code_obj.used_at = int(time.time())
        auth_code_obj.user_id = user_id
        auth_code_obj.save()

        # 创建新任务
        copyright_task = CopyrightTask(
            user_id=user_id,
            name=name,
            description=description,
            status="生成中",
            progress=0,
            create_time=int(time.time())
        )
        copyright_task.save()
        task_id = str(copyright_task.id)
        
        # 在后台线程中运行工作流
        # run_workflow1 函数内部有完整的异常处理逻辑，因此这里不需要额外的 try-except
        executor = ThreadPoolExecutor(max_workers=1)
        executor.submit(run_workflow1, description, task_id)
        executor.shutdown(wait=False)
        
        olog.info(f"已提交软件著作权生成任务, 任务ID: {task_id}")
        return doc_to_dict(copyright_task)

    @auth_required(["user", "admin"])
    def query_list(self, data):
        user_id = data.get("user_id")  # 用户ID会自动注入
        page = int(data.get("page", 1))
        page_size = int(data.get("page_size", 3))

        # 构建查询条件，只查询当前用户的任务
        query = {"user_id": user_id}

        # 计算总数
        total = CopyrightTask.objects(**query).count()

        # 获取分页数据，按创建时间倒序排列
        tasks = CopyrightTask.objects(**query).order_by("-create_time").skip((page - 1) * page_size).limit(page_size)
        
        # 转换为字典列表
        tasks_dict = docs_to_dict(tasks)
        
        # 为已完成且有文档的任务生成下载URL
        oss_client = None
        for task_dict in tasks_dict:
            if task_dict.get("status") == "已完成" and task_dict.get("document_key"):
                try:
                    # 懒加载OSS客户端
                    if oss_client is None:
                        oss_client = OSSClient.getInstance()
                    
                    # 生成临时下载URL，有效期7天
                    download_url = oss_client.signed_url(SignedMethod.GET, task_dict["document_key"])
                    task_dict["download_url"] = download_url
                except Exception as e:
                    olog.exception(f"生成下载链接失败: {str(e)}")
                    # 下载链接生成失败不影响任务信息返回

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": tasks_dict
        }

    @auth_required(["user", "admin"])
    def ai_expand_description(self, data):
        olog.info("开始处理AI智能完善描述请求")
        # 获取原始描述
        description = data.get("description")

        if not description:
            olog.warning("AI扩展描述失败：原始描述为空")
            raise MException("原始描述不能为空")

        olog.debug(f"原始描述长度: {len(description)}")

        try:
            olog.debug("准备调用LLM服务")

            # 构建提示词
            prompt_template = """
            # 角色
            你是一位资深的需求分析师

            # 背景
            用户提供的软件描述：
            {description}

            # 任务
            根据用户提供的软件简述，扩展并完善为一个详细的软件功能描述

            # 约束
            - 扩展后的内容必须基于原始描述，不得编造与原始描述不符的功能
            - 使用专业、正式的语言风格
            - 内容要全面但不冗余

            # 输出格式
            不要使用markdown格式,返回需求描述的字符串.
            """

            # 使用LangChain创建提示模板
            prompt = ChatPromptTemplate.from_template(prompt_template)
            # 获取LLM模型
            llm = ChatModelFactory.get_llm()
            # 创建输出解析器
            parser = StrOutputParser()

            # 构建处理链
            chain = prompt | llm | parser
            chain = chain.with_retry()
            
            # 执行链并获取结果
            params = {"description": description}
            expanded_description = chain.invoke(params)

            # 合并原始描述和扩展描述
            final_description = f"{expanded_description}"

            olog.info("AI扩展描述成功")
            olog.debug(f"扩展后描述长度: {len(final_description)}")

            return {
                "expanded_description": final_description
            }
        except Exception as e:
            olog.exception(f"AI扩展描述时发生异常: {str(e)}")
            raise MException(f"AI扩展描述失败: {str(e)}")
